#include <iostream> 
using namespace std; 
int main() { 
double num1, num2; 
char operation; 
// Displaying the menu to the user 
cout << "Simple Calculator\n"; 
cout << "Enter two numbers: "; 
cin >> num1 >> num2; 
cout << "Enter an operator (+, -, *, /): "; 
cin >> operation; 
// Performing the operation based on the user's choice 
switch (operation) { 
case '+': 
cout << "Result: " << num1 + num2 << endl; 
break; 
case '-': 
cout << "Result: " << num1 - num2 << endl; 
break; 
case '*': 
cout << "Result: " << num1 * num2 << endl; 
break; 
case '/': 
// Handling division by zero 
if (num2 != 0) { 
cout << "Result: " << num1 / num2 << endl; 
} else { 
cout << "Error! Division by zero.\n"; 
} 
break; 
default: 
cout << "Error! Invalid operator.\n"; 
break; 
} 
return 0; 
}